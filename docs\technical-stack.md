# Technical Stack Documentation

## System Architecture Overview

### Frontend Components
1. **Excel Add-in**
   - Framework: React 19+ with TypeScript
   - UI Library: Fluent UI React v9 (@fluentui/react-components)
   - Design System: Fluent 2
   - Office Integration: Office.js (ExcelApi 1.17+)
   - State Management: Zustand
   - Build Tools: Webpack

2. **Web Application**
   - Framework: Next.js (App Router)
   - Styling: Tailwind CSS + shadcn/ui
   - State Management: Zustand
   - Build & Deploy: Vercel Edge Runtime

### Backend Infrastructure
1. **Core Backend (Supabase)**
   - Database: PostgreSQL
   - Authentication: Supabase Auth
   - Real-time: Supabase Realtime
   - Storage: Supabase Storage
   - Functions: Edge Functions
   - Security: Row Level Security (RLS)

2. **API Layer**
   - Protocol: tRPC
   - Runtime: Edge Functions
   - Type Safety: End-to-end type safety with tRPC

### AI Infrastructure
1. **Agent Framework (Agno)**
   - Multi-Agent Architecture
     - Advanced reasoning capabilities
     - Multi-modal support (text, image, audio, video)
     - Built-in memory and session storage
     - Vector database integration (20+ supported)
   - Model Integration
     - Model-agnostic (23+ providers)
     - Structured outputs with JSON mode
     - Native FastAPI integration
   - Performance Features
     - Ultra-fast instantiation (~3μs)
     - Low memory footprint (~5Kib per agent)
     - Built-in monitoring via agno.com
   - Development Features
     - Production-ready agent systems
     - Built-in RAG capabilities
     - Team-based agent collaboration
     - State management and persistence

2. **Specialized Agents**
Vision Agent:
- OCR processing for images and documents
- Table extraction from PDFs and images
- Receipt and invoice scanning
- Document layout analysis
- Image preprocessing and enhancement
- Batch image processing capabilities

File Processing Agent:
- Local file system integration
- Cloud storage integration (Google Drive, OneDrive)
- PDF parsing and text extraction
- CSV processing and transformation
- Batch file processing
- File format conversion and standardization

Data Analysis Agent:
Natural language processing for data analysis queries
Statistical analysis and hypothesis testing
Pattern recognition
Time series analysis and forecasting
Automated outlier detection
Cross-tabulation and pivot analysis
Data Cleaning Agent:
Automated data cleaning workflows
Missing value handling
Duplicate detection and removal
Format standardization
Data validation rules
Batch processing
Visualization Agent:
Smart chart and graph recommendations
Custom visualization generation
Interactive dashboard creation
African market-specific visualization templates
Export formatting for presentations
Formula Generation Agent:
Natural language to Excel formula conversion
Formula optimization
Complex formula explanation
Custom function generation
Error checking and validation
Python Code Agent:
Safe Python code generation
Code execution in sandbox environment
Statistical modeling
Advanced analytics functions
Integration with Excel data
Language Processing Agent:
Bilingual support (English/French)
Voice command processing
Context-aware conversations
Multi-turn analysis memory
Natural language query parsing
Data Integration Agent:
Excel file access and manipulation
OneDrive/Office 365 integration
External data source connections (SQL, Google Sheets, Oracle)
Web data extraction
Data enrichment through web research

3. **Agent Orchestration**
   - Task Distribution
   - Context Sharing
   - Error Handling
   - State Management
   - Agent Communication

### Analytics & Data Processing
1. **Analytics Engine**
   - Data Processing: pandas, numpy
   - Statistical Analysis: scipy, statsmodels
   - Machine Learning: scikit-learn
   - Visualization: matplotlib, seaborn
   - NLP: spaCy, NLTK, Transformers

2. **Sandbox Environment (Daytona)**
   - Isolation & Security
     - Container-based environments
     - Code static analysis
     - Input validation
     - Execution timeouts
     - Secure result channels
   - Resource Management
     - Memory limits
     - CPU restrictions
     - Disk space quotas
     - Network isolation
   - Development Features
     - Local testing support
     - Debug capabilities
     - Environment reproduction
     - Version control integration

3. **Data Analysis Features**
   - Statistical Analysis
   - Time Series Analysis
   - Forecasting
   - Pattern Recognition
   - Outlier Detection
   - Data Cleaning

### Integration & APIs
1. **Database Connectors**
   - SQL Databases
   - Oracle
   - Google Sheets
   - Web Data Extraction

2. **External Services**
   - PostHog (Analytics)
   - OpenReplay (Session Recording)
   - Sentry (Error Tracking)
   - Upstash (Rate Limiting)

### Security & Compliance
1. **Authentication & Authorization**
   - Supabase Auth
   - OAuth Integration
   - Row Level Security
   - RBAC Implementation

2. **Data Protection**
   - End-to-end Encryption
   - At-rest Encryption
   - Input Validation
   - Output Sanitization
   - Rate Limiting
   - Audit Logging

3. **Compliance**
   - SOC II Compliance
   - GDPR Compliance
   - African Data Protection Laws

### Performance & Infrastructure
1. **Edge Computing**
   - Regional Edge Functions
   - CDN Integration
   - Connection Pooling
   - Query Optimization

2. **Caching Strategy**
   - Client-side Cache
   - CDN Cache
   - Database Cache
   - Static Asset Optimization

3. **Regional Optimization**
   - African Market Support
     - Regional Edge Functions
     - Bandwidth Optimization
     - Local Payment Processing
     - Regional Compliance
     - Language Support (EN/FR)
   - Infrastructure
     - Regional Database Instances
     - CDN Points of Presence
     - Connection Optimization
     - Cache Distribution

### Development & Quality
1. **Testing Infrastructure**
   - Unit Tests: Vitest
   - Component Tests: React Testing Library
   - E2E Tests: Playwright
   - Visual Testing: Storybook

2. **CI/CD**
   - Platform: GitHub Actions
   - Deployment: Vercel Pipeline
   - Quality Gates: Coverage, Performance, Security

3. **Code Quality**
   - TypeScript 5.x+ (Strict Mode)
   - ESLint
   - Prettier
   - Husky Pre-commit Hooks

4. **Documentation**
   - API: OpenAPI/Swagger
   - Code: TSDoc/JSDoc
   - Platform: Mintlify
   - Search: AI-powered docs search

### Monitoring & Analytics
1. **Performance Monitoring**
   - RUM (Real User Monitoring)
   - Core Web Vitals
   - Custom Metrics
   - Regional Performance

2. **Error Tracking**
   - Sentry Integration
   - Error Aggregation
   - Alert System
   - Error Analytics
